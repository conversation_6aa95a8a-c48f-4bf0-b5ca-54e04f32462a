
================================================================================
JSON TOUCH DATA INSPECTION REPORT
================================================================================

File: Tracing_2025-04-03 11_44_41.266250_67ed65c92d5fe54f0102f577.json
Path: /Users/<USER>/Desktop/Kidaura/cleaning_pipe/raw_JSONs/Tracing_2025-04-03 11_44_41.266250_67ed65c92d5fe54f0102f577.json
Size: 455,028 bytes
Last Modified: 2025-05-18 17:29:13.389285

Data Type: Tracing

Overall Structure:
  - Root keys: message, json
  - JSON keys: dataSet, touchData, startTime, maxTapCount, gameFinished, endTime

Touch Data Structure:
  The touchData object is organized as follows:
  - Each key in touchData represents a distinct finger ID
  - Each finger ID contains an array of touch points
  - Each touch point contains various attributes (x, y, time, etc.)

  Visualization of the JSON structure:
  message
  json
    ├── dataSet
    ├── touchData
        ├── 0 (Finger ID)
        │    └── [22 touch points]
        │         └── Sample touch point:
        │              ├── x: 639.3577
        │              ├── y: 653.8455
        │              ├── time: 3246.83057
        │              ├── touchPhase: S
        │              ├── accx: 0.0607699864
        │              ├── accy: 0.09544544
        │              ├── accz: -0.9864684
        │              ├── zone: None
        │              ├── distance: 0.0
        │              ├── camFrame: 0
        │              ├── isDragging: False
        ├── 1 (Finger ID)
        │    └── [1 touch points]
        ├── 2 (Finger ID)
        │    └── [296 touch points]
        ├── 3 (Finger ID)
        │    └── [1 touch points]
        ├── 4 (Finger ID)
        │    └── [170 touch points]
        ├── 5 (Finger ID)
        │    └── [1 touch points]
        ├── 6 (Finger ID)
        │    └── [75 touch points]
        ├── 7 (Finger ID)
        │    └── [1 touch points]
        ├── 8 (Finger ID)
        │    └── [177 touch points]
        ├── 9 (Finger ID)
        │    └── [1 touch points]
        ├── 10 (Finger ID)
        │    └── [87 touch points]
        ├── 11 (Finger ID)
        │    └── [1 touch points]
        ├── 12 (Finger ID)
        │    └── [41 touch points]
        ├── 13 (Finger ID)
        │    └── [1 touch points]
        ├── 14 (Finger ID)
        │    └── [17 touch points]
        ├── 15 (Finger ID)
        │    └── [1 touch points]
        ├── 16 (Finger ID)
        │    └── [37 touch points]
        ├── 17 (Finger ID)
        │    └── [1 touch points]
        ├── 18 (Finger ID)
        │    └── [103 touch points]
        ├── 19 (Finger ID)
        │    └── [1 touch points]
        ├── 20 (Finger ID)
        │    └── [41 touch points]
        ├── 21 (Finger ID)
        │    └── [1 touch points]
        ├── 22 (Finger ID)
        │    └── [37 touch points]
        ├── 23 (Finger ID)
        │    └── [1 touch points]
        ├── 24 (Finger ID)
        │    └── [19 touch points]
        ├── 25 (Finger ID)
        │    └── [1 touch points]
        ├── 26 (Finger ID)
        │    └── [9 touch points]
        ├── 27 (Finger ID)
        │    └── [1 touch points]
        ├── 28 (Finger ID)
        │    └── [143 touch points]
        ├── 29 (Finger ID)
        │    └── [1 touch points]
        ├── 30 (Finger ID)
        │    └── [47 touch points]
        ├── 31 (Finger ID)
        │    └── [1 touch points]
        ├── 32 (Finger ID)
        │    └── [147 touch points]
        ├── 33 (Finger ID)
        │    └── [1 touch points]
        ├── 34 (Finger ID)
        │    └── [63 touch points]
        ├── 35 (Finger ID)
        │    └── [1 touch points]
        ├── 36 (Finger ID)
        │    └── [127 touch points]
        ├── 37 (Finger ID)
        │    └── [1 touch points]
        ├── 38 (Finger ID)
        │    └── [7 touch points]
        ├── 39 (Finger ID)
        │    └── [1 touch points]
        ├── 40 (Finger ID)
        │    └── [57 touch points]
        ├── 41 (Finger ID)
        │    └── [1 touch points]
        ├── 42 (Finger ID)
        │    └── [125 touch points]
        ├── 43 (Finger ID)
        │    └── [1 touch points]
        ├── 44 (Finger ID)
        │    └── [91 touch points]
        ├── 45 (Finger ID)
        │    └── [1 touch points]
        ├── 46 (Finger ID)
        │    └── [174 touch points]
        ├── 47 (Finger ID)
        │    └── [1 touch points]
        ├── 48 (Finger ID)
        │    └── [246 touch points]
        ├── 49 (Finger ID)
        │    └── [1 touch points]
        └── 50 (Finger ID)
            └── [60 touch points]
    ├── startTime
    ├── maxTapCount
    ├── gameFinished
    └── endTime

Touch Data Statistics:
  - Number of distinct finger IDs: 51 (unique keys in the touchData object)
  - Total touch points: 2,443 (sum of all touch events across all finger IDs)

Chronological Sorting Analysis:
[92m[1m  - Status: ✓ Data is chronologically sorted by time[0m
  - All 2443 touch points are in chronological order
  - Average touch points per finger ID: 47.90
  - Median touch points per finger ID: 7.00

Touch Phases:
  - S: 181 (7.4%)
  - M: 2,152 (88.1%)
  - E: 56 (2.3%)
  - B: 54 (2.2%)

Zones:
  - Car: 1,732 (70.9%)
  - Outside: 689 (28.2%)

Time Statistics (in milliseconds):
  - Range: 3246.83 to 3309.92
  - Duration: 63.09
  - Mean: 3278.04
  - Median: 3277.07

Distance Statistics:
  - Range: 0.00 to 1102.17
  - Total distance: 1102.17
  - Mean: 636.96
  - Median: 645.52

All Data Fields:
  accx, accy, accz, camFrame, distance, isDragging, time, touchPhase, x, y, zone

Sample Data Entry:
  - x: 639.3577
  - y: 653.8455
  - time: 3246.83057
  - touchPhase: S
  - accx: 0.0607699864
  - accy: 0.09544544
  - accz: -0.9864684
  - zone: None
  - distance: 0.0
  - camFrame: 0
  - isDragging: False

Field Explanations:
  - x: X-coordinate of the touch point on the screen
  - y: Y-coordinate of the touch point on the screen
  - time: Timestamp of the touch event in milliseconds
  - touchPhase: Phase of the touch event (Began/B, Moved/M, Ended/S, Stationary)
  - zone: Area of the screen where the touch occurred
  - accx: Accelerometer X-axis reading
  - accy: Accelerometer Y-axis reading
  - accz: Accelerometer Z-axis reading
  - distance: Distance traced from the starting point
  - camFrame: Camera frame identifier
  - isDragging: Whether the touch is a dragging motion

Touch Phase Explanation:
  - B (Began): Touch has just started
  - M (Moved): Touch position has changed
  - S (Ended): Touch has ended

Context for Children's Touch Interactions:
  This data represents a child's tracing activity on a touch screen.
  - Each touch point represents where the child placed their finger.
  - The 'distance' field shows how far the child has traced from the start.
  - The 'zone' field indicates which object was being traced.
  - 'isDragging' indicates whether the child was dragging their finger.

================================================================================