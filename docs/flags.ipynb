{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Touch Data Flagging System: Visual Guide\n", "\n", "This notebook provides a visual explanation of how our touch data processing pipeline identifies and flags sequence issues."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON>id Touch Sequence\n", "\n", "A valid touch sequence represents a complete finger interaction from touch to lift. It follows a specific pattern:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Coloring Data Valid Sequence\n", "\n", "```\n", "┌─────────┐     ┌─────────┐     ┌─────────┐     ┌─────────┐\n", "│  Began  │ ──► │  Moved  │ ──► │  Moved  │ ──► │  Ended  │\n", "└─────────┘     └─────────┘     └─────────┘     └─────────┘\n", "    │               │               │               │\n", "    ▼               ▼               ▼               ▼\n", "Finger down     Movement        Movement        Finger up\n", "```\n", "\n", "**Required Elements:**\n", "- **Begin Event**: `touchPhase = \"Began\"` - When finger first touches screen\n", "- **Middle Events**: `touchPhase = \"Moved\"` or `\"Stationary\"` - As finger moves or stays still\n", "- **End Event**: `touchPhase = \"Ended\"` or `\"Canceled\"` - When finger is lifted"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Sequence ID Assignment\n", "\n", "Each touch event is assigned a `seqId` (sequence identifier) that groups related events together."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### How Sequence IDs Work\n", "\n", "```\n", "┌─────────────────────────────────────────────────────────┐\n", "│ fingerId = 0                                            │\n", "├───────────┬───────────┬───────────┬───────────┬─────────┤\n", "│ seqId = 1 │ seqId = 2 │ seqId = 3 │ seqId = 4 │   ...   │\n", "└───────────┴───────────┴───────────┴───────────┴─────────┘\n", "\n", "┌─────────────────────────────────────────────────────────┐\n", "│ fingerId = 1                                            │\n", "├───────────┬───────────┬───────────┬───────────┬─────────┤\n", "│ seqId = 1 │ seqId = 2 │ seqId = 3 │ seqId = 4 │   ...   │\n", "└───────────┴───────────┴───────────┴───────────┴─────────┘\n", "```\n", "\n", "**Key Points:**\n", "- Each finger (`fingerId`) has its own independent sequence numbering\n", "- Sequences are numbered sequentially starting from 1\n", "- `seqId = 0` is reserved for events that don't belong to any valid sequence\n", "- A new sequence begins only after the current sequence has properly ended"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Multi-Finger Example\n", "\n", "```\n", "Time ─────────────────────────────────────────────────────────────►\n", "\n", "Finger 0: [Sequence 1]───[Sequence 2]───[Sequence 3]\n", "           B→M→M→E        B→M→E          B→M→M→M→E\n", "\n", "Finger 1:      [Sequence 1]───────[Sequence 2]\n", "                B→M→M→M→E          B→M→E\n", "```\n", "\n", "**Note:** Each finger maintains its own sequence numbering, allowing for multi-touch interactions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Flag Visual Reference Guide\n", "\n", "Our system applies flags to sequences that don't follow the expected pattern. Here's a visual guide to each flag:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `missing_Began` Flag\n", "\n", "```\n", "      ●───────●───────●───────●───────●───────●───────●\n", "      │       │       │       │       │       │       │\n", "      │       │       │       │       │       │       │\n", "    Moved   Moved   Moved   Moved   Moved   Moved   Ended\n", "    \n", "    ⚠️ missing_<PERSON>gan\n", "```\n", "\n", "**Plain English:** The sequence is missing its starting point.\n", "\n", "**Common Cause:** Touch began outside the active sensing area of the screen."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `missing_Ended` Flag\n", "\n", "```\n", "  ●───────●───────●───────●───────●───────●───────●\n", "  │       │       │       │       │       │       │\n", "  │       │       │       │       │       │       │\n", "Began   Moved   Moved   Moved   Moved   Moved   Moved\n", "  \n", "  ⚠️ missing_Ended\n", "```\n", "\n", "**Plain English:** The sequence never properly ended.\n", "\n", "**Common Cause:** User lifted their finger outside the active sensing area."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `multiple_end_events` Flag\n", "\n", "```\n", "  ●───────●───────●───────●───────●───────●───────●───────●\n", "  │       │       │       │       │       │       │       │\n", "  │       │       │       │       │       │       │       │\n", "Began   Moved   Moved   Ended   Moved   Moved   Moved   Ended\n", "  \n", "  ⚠️ multiple_end_events\n", "```\n", "\n", "**Plain English:** The sequence has more than one ending event.\n", "\n", "**Common Cause:** Touch sensor incorrectly detected finger lift and then redetected the same touch."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `sequence_interrupted` Flag\n", "\n", "```\n", "  ●───────●───────●───────●───────●\n", "  │       │       │       │       │\n", "  │       │       │       │       │\n", "<PERSON>gan   Moved   Moved   Moved   Began (new sequence)\n", "  \n", "  ⚠️ sequence_interrupted\n", "```\n", "\n", "**Plain English:** A new sequence started before the current one ended.\n", "\n", "**Common Cause:** Hardware issue causing \"phantom\" begin events."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `orphaned_events` Flag\n", "\n", "```\n", "  ●───────●───────●───────●       ●───────●───────●───────●\n", "  │       │       │       │       │       │       │       │\n", "  │       │       │       │       │       │       │       │\n", "Began   Moved   Moved   Ended   Moved   Moved   Moved   Began (new sequence)\n", "                              │       │       │\n", "                              └───────┴───────┘\n", "                               Orphaned events\n", "  \n", "  ⚠️ orphaned_events\n", "```\n", "\n", "**Plain English:** Touch events occurred between sequences, not belonging to any sequence.\n", "\n", "**Common Cause:** Touch sensor detected movement after the finger was lifted."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `short_duration` Flag\n", "\n", "```\n", "  ●───●\n", "  │   │\n", "  │   │\n", "<PERSON><PERSON> Ended\n", "  \n", "  ⚠️ short_duration (< 0.01 seconds)\n", "```\n", "\n", "**Plain English:** The touch sequence was extremely brief (less than 10ms).\n", "\n", "**Common Cause:** User accidentally brushed against the screen."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `too_few_points` Flag\n", "\n", "```\n", "  ●───────●\n", "  │       │\n", "  │       │\n", "<PERSON><PERSON>   Ended\n", "  \n", "  ⚠️ too_few_points (< 3 points)\n", "```\n", "\n", "**Plain English:** The sequence has fewer than 3 touch events.\n", "\n", "**Common Cause:** User performed a quick tap rather than a drag or swipe."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `sequence_gap` Flag\n", "\n", "```\n", "  ●───────●         ●───────●───────●\n", "  │       │         │       │       │\n", "  │       │         │       │       │\n", "Began   Moved     Moved   Moved   Ended\n", "            \\     /\n", "             \\   /\n", "              GAP\n", "             >100ms\n", "  \n", "  ⚠️ sequence_gap\n", "```\n", "\n", "**Plain English:** There was a significant time gap (>100ms) between events in a sequence.\n", "\n", "**Common Cause:** Application experienced performance issues or frame drops."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `improper_sequence_order` Flag\n", "\n", "```\n", "  ●───────●───────●───────●\n", "  │       │       │       │\n", "  │       │       │       │\n", "Began   Ended   Moved   Moved\n", "  \n", "  ⚠️ improper_sequence_order\n", "```\n", "\n", "**Plain English:** Touch events don't follow the expected order (Began → Moved/Stationary → Ended).\n", "\n", "**Common Cause:** Touch events were processed out of order due to threading or timing issues."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Flag Precedence Hierarchy\n", "\n", "When conflicts occur between flags, the system resolves them using this priority hierarchy:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```\n", "┌─────────────────────┐\n", "│  orphaned_events    │  Priority 1 (Highest)\n", "└──────────┬──────────┘\n", "           ▼\n", "┌─────────────────────┐\n", "│sequence_interrupted │  Priority 2\n", "└──────────┬──────────┘\n", "           ▼\n", "┌─────────────────────┐\n", "│ multiple_end_events │  Priority 3\n", "└──────────┬──────────┘\n", "           ▼\n", "┌─────────────────────┐\n", "│    missing_<PERSON>gan    │  Priority 4\n", "└──────────┬──────────┘\n", "           ▼\n", "┌─────────────────────┐\n", "│    missing_Ended    │  Priority 5\n", "└──────────┬──────────┘\n", "           ▼\n", "┌─────────────────────┐\n", "│improper_sequence_order│ Priority 6\n", "└──────────┬──────────┘\n", "           ▼\n", "┌─────────────────────┐\n", "│   short_duration    │  Priority 7\n", "└──────────┬──────────┘\n", "           ▼\n", "┌─────────────────────┐\n", "│    too_few_points   │  Priority 8\n", "└──────────┬──────────┘\n", "           ▼\n", "┌─────────────────────┐\n", "│     has_canceled    │  Priority 9\n", "└──────────┬──────────┘\n", "           ▼\n", "┌─────────────────────┐\n", "│    sequence_gap     │  Priority 10 (Lowest)\n", "└─────────────────────┘\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Conflict Resolution Examples\n", "\n", "| Conflicting Flags | Resolution | Reason |\n", "|-------------------|------------|--------|\n", "| `missing_Ended` + `multiple_end_events` | Keep `multiple_end_events` | Having multiple end events is more specific than saying an end is missing |\n", "| `missing_Began` + `orphaned_events` | Keep `orphaned_events` | Orphaned events explain why a beginning might be missing |\n", "| `sequence_interrupted` + `missing_Ended` | Keep `sequence_interrupted` | An interruption explains why an end might be missing |"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}