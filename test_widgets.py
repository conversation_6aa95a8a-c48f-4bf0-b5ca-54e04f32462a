#!/usr/bin/env python3
"""
Test script to isolate matplotlib widgets issue
"""

import matplotlib.pyplot as plt
from matplotlib.widgets import CheckButtons, RadioButtons, Button
import numpy as np

def test_widgets():
    """Test matplotlib widgets to identify the issue."""
    print("Testing matplotlib widgets...")
    
    # Create a simple figure
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # Create some test data
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    line, = ax.plot(x, y)
    
    # Test CheckButtons
    print("Testing CheckButtons...")
    try:
        # Create axes for checkboxes
        ax_check = plt.axes([0.85, 0.7, 0.1, 0.15])
        labels = ['Sin', 'Cos', 'Tan']
        check = CheckButtons(ax_check, labels, [True, False, False])
        print("✓ CheckButtons created successfully")
        
        def func(label):
            if label == 'Sin':
                line.set_ydata(np.sin(x))
            elif label == 'Cos':
                line.set_ydata(np.cos(x))
            elif label == 'Tan':
                line.set_ydata(np.tan(x))
            plt.draw()
        
        check.on_clicked(func)
        
    except Exception as e:
        print(f"❌ CheckButtons failed: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
    
    # Test RadioButtons
    print("Testing RadioButtons...")
    try:
        # Create axes for radio buttons
        ax_radio = plt.axes([0.85, 0.4, 0.1, 0.15])
        radio_labels = ['Red', 'Blue', 'Green']
        radio = RadioButtons(ax_radio, radio_labels)
        print("✓ RadioButtons created successfully")
        
        def color_func(label):
            if label == 'Red':
                line.set_color('red')
            elif label == 'Blue':
                line.set_color('blue')
            elif label == 'Green':
                line.set_color('green')
            plt.draw()
        
        radio.on_clicked(color_func)
        
    except Exception as e:
        print(f"❌ RadioButtons failed: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
    
    # Test Button
    print("Testing Button...")
    try:
        # Create axes for button
        ax_button = plt.axes([0.85, 0.1, 0.1, 0.05])
        button = Button(ax_button, 'Reset')
        print("✓ Button created successfully")
        
        def reset_func(event):
            line.set_ydata(np.sin(x))
            line.set_color('blue')
            plt.draw()
        
        button.on_clicked(reset_func)
        
    except Exception as e:
        print(f"❌ Button failed: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
    
    ax.set_title('Widget Test - Check console for results')
    ax.grid(True)
    
    print("\nWidget test complete. Check the plot window.")
    print("If widgets work, you should see checkboxes, radio buttons, and a button on the right side.")
    
    plt.show()

if __name__ == "__main__":
    # Print matplotlib info
    import matplotlib
    print(f"Matplotlib version: {matplotlib.__version__}")
    print(f"Backend: {plt.get_backend()}")

    test_widgets()
