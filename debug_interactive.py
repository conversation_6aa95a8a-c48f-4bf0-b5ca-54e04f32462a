#!/usr/bin/env python3
"""
Debug script to test the specific interactive visualization issue
"""

import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.widgets import CheckButtons, RadioButtons, Button
import numpy as np
import os

def debug_interactive_viz():
    """Debug the interactive visualization issue."""
    print("Debugging interactive visualization...")

    # Import the load_data function from views.py
    import sys
    sys.path.append('.')
    from views import load_data, parse_flags_from_string, get_sequence_flags

    # Load a sample CSV file
    csv_file = "flagged_data/Coloring_2025-04-03 11_41_53.821624_67ed65c92d5fe54f0102f579.csv"

    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return

    print(f"Loading data using views.load_data()...")
    try:
        df, seq_metrics = load_data(csv_file)
        print(f"✓ Data loaded successfully")
        print(f"Data shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")

        # Check the flags column after processing
        if 'flags' in df.columns:
            print(f"Flags column type after load_data: {type(df['flags'].iloc[0])}")
            print(f"Sample flags after load_data: {df['flags'].head()}")

            # Test the helper functions
            print("\nTesting helper functions...")
            sample_flags = df['flags'].iloc[0]
            print(f"Sample flags: {sample_flags}")
            parsed = parse_flags_from_string(sample_flags)
            print(f"Parsed flags: {parsed}")

            # Test on a small subset
            print("\nTesting on small subset...")
            small_df = df.head(10)
            seq_flags = get_sequence_flags(small_df)
            print(f"Sequence flags from subset: {seq_flags}")

            # Try to extract all flags like in the original code
            print("\nExtracting all flags...")
            all_flags = set()
            for i, flags in enumerate(df['flags']):
                if i < 5:  # Only show first 5 for debugging
                    print(f"Processing flags {i}: {flags} (type: {type(flags)})")

                flag_list = parse_flags_from_string(flags)
                all_flags.update(flag_list)

                if i > 100:  # Limit processing for debugging
                    break

            all_flags = sorted(list(all_flags))
            print(f"All flags found: {all_flags}")

            # Now test creating CheckButtons with these flags
            print("\nTesting CheckButtons with actual flags...")

            try:
                fig, ax = plt.subplots(figsize=(10, 6))

                # Create a subplot for checkboxes
                ax_check = plt.axes([0.85, 0.5, 0.1, 0.3])

                print(f"Creating CheckButtons with flags: {all_flags}")
                print(f"Number of flags: {len(all_flags)}")
                print(f"Actives list: {[True] * len(all_flags)}")

                check = CheckButtons(ax_check, all_flags, [True] * len(all_flags))
                print("✓ CheckButtons created successfully with actual data")

                ax.set_title('CheckButtons test with real data')
                plt.show()

            except Exception as e:
                print(f"❌ CheckButtons failed with real data: {e}")
                print(f"Error type: {type(e).__name__}")
                import traceback
                traceback.print_exc()

        else:
            print("❌ No 'flags' column found")

    except Exception as e:
        print(f"❌ Error loading data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_interactive_viz()
